import { router } from 'expo-router';
import React, { useCallback, useImperativeHandle, useState } from 'react';
import Modal from 'react-native-modal';
import { Button, Text, View } from 'tamagui';

export type AdLockConfirmModalRef = {
  show: () => void;
  close: () => void;
};

type Props = {
  handleWatchAd: () => void;
};

/**
 * 广告锁确认弹窗
 */
export const AdLockConfirmModal = React.forwardRef<
  AdLockConfirmModalRef,
  Props
>((props, ref) => {
  const { handleWatchAd } = props;
  const [visible, setVisible] = useState(false);

  useImperativeHandle(ref, () => ({
    show: () => {
      setVisible(true);
    },
    close: () => {
      setVisible(false);
    },
  }));

  const onPressClose = useCallback(() => {
    setVisible(false);
    router.back();
  }, []);

  return (
    <Modal isVisible={visible} hasBackdrop statusBarTranslucent>
      <View className={'rounded-2xl bg-white p-6'}>
        <Text className='text-center text-lg font-bold'>
          今日免费查看答案次数已用完，看广告解锁本书
        </Text>
        <View className='mt-8 w-full flex-row gap-x-2 px-6'>
          <View className='flex-1'>
            <Button
              className='text-primary rounded-full bg-white'
              onPress={onPressClose}
            >
              取消
            </Button>
          </View>
          <View className='flex-1'>
            <Button
              className='bg-primary rounded-full text-white'
              onPress={handleWatchAd}
            >
              看广告解锁
            </Button>
          </View>
        </View>
      </View>
    </Modal>
  );
});

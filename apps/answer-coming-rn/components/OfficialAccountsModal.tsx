import {
  YT<PERSON>utton,
  YTImage,
  YTText,
  YTYStack,
  YTXStack,
} from '@bookln/cross-platform-components';
import { container } from '@jgl/container';
import { showToast } from '@jgl/utils';
import { X } from '@tamagui/lucide-icons';
import { YTRequest } from '@yunti-private/net';
import { useMount } from 'ahooks';
import { cacheDirectory, downloadAsync } from 'expo-file-system';
import {
  requestPermissionsAsync,
  saveToLibraryAsync,
} from 'expo-media-library';
import {
  forwardRef,
  memo,
  useCallback,
  useImperativeHandle,
  useState,
} from 'react';
import Modal from 'react-native-modal';
import { StyleSheet } from 'react-native';

export type OfficialAccountsModalRef = {
  showModal: () => void;
};

type Props = {
  afterClose?: () => void;
};

/** 获取关注公众号二维码 */
const genSubscribeQrCode = (): YTRequest<string> => {
  return {
    url: '/dallWx/genSubscribeQrCode.do',
  };
};

/** 关注公众号弹窗 */
export const OfficialAccountsModal = memo(
  forwardRef<OfficialAccountsModalRef, Props>((props, ref) => {
    const { afterClose } = props;
    const [open, setOpen] = useState(false);
    const [url, setUrl] = useState<string>('');
    const [loading, setLoading] = useState(false);

    const onClose = useCallback(() => {
      setOpen(false);
      afterClose?.();
    }, [afterClose]);

    const getQrCode = useCallback(async () => {
      try {
        setLoading(true);
        const res = await container.net().fetch(genSubscribeQrCode());
        const { success, data } = res;

        if (success && data) {
          setUrl(data);
        }
      } catch (error) {
        console.error('获取公众号二维码失败:', error);
      } finally {
        setLoading(false);
      }
    }, []);

    const onClickOpen = useCallback(() => {
      setOpen(true);
      getQrCode();
    }, [getQrCode]);

    /** 保存二维码到相册 */
    const saveQrCodeToPhotoLibrary = useCallback(async () => {
      if (!url) {
        setOpen(false);
        showToast({ title: '二维码未加载完成' });
        return;
      }

      try {
        // 暂时使用ContactServiceModal中成功的权限检查方式
        const { status } = await requestPermissionsAsync();
        if (status !== 'granted') {
          setOpen(false);
          showToast({ title: '需要相册权限才能保存图片' });
          return;
        }

        // 如果是网络图片，先下载到本地缓存
        let localUri = url;
        if (/^https?:\/\//.test(url)) {
          const fileName = `official_qr_${Date.now()}.jpg`;
          const dest = `${cacheDirectory}${fileName}`;
          const downloadResult = await downloadAsync(url, dest);
          if (downloadResult.status !== 200) {
            throw new Error('图片下载失败');
          }
          localUri = downloadResult.uri;
        }

        // 保存图片到相册（需要本地文件 URI）
        await saveToLibraryAsync(localUri);
        setOpen(false);
        showToast({ title: '已保存至相册' });
      } catch (error) {
        console.error('保存二维码失败:', error);
        const errorMessage =
          error instanceof Error ? error.message : '保存失败';
        showToast({ title: errorMessage });
      }
    }, [url]);

    useImperativeHandle(ref, () => ({
      showModal: onClickOpen,
    }));

    useMount(() => {
      getQrCode();
    });
    const styles = StyleSheet.create({
      modalContainer: {
        justifyContent: 'center',
        alignItems: 'center',
      },
    });
    return (
      <>
        <Modal
          onBackdropPress={onClose}
          isVisible={open}
          animationIn='fadeIn'
          animationOut='fadeOut'
          style={styles.modalContainer}
          statusBarTranslucent
        >
          <YTYStack
            maxWidth={327}
            overflow='hidden'
            borderRadius={16}
            backgroundColor='white'
          >
            <YTXStack
              height={56}
              width='100%'
              flexDirection='row'
              alignItems='center'
              justifyContent='space-between'
              paddingHorizontal={12}
              bg='white'
            >
              <YTYStack height={32} width={32} />

              <YTText fontWeight='bold' fontSize={16}>
                关注公众号
              </YTText>

              <YTYStack
                height={32}
                width={32}
                alignItems='center'
                justifyContent='center'
                onPress={onClose}
                bg='white'
              >
                <X size={18} color='#666' />
              </YTYStack>
            </YTXStack>

            <YTXStack
              width='100%'
              justifyContent='center'
              alignItems='center'
              flexDirection='row'
              bg='white'
            >
              <YTImage height={180} width={180} source={{ uri: url }} />
            </YTXStack>

            <YTYStack
              alignItems='center'
              justifyContent='center'
              paddingBottom={24}
              bg='white'
            >
              <YTText color='#8B8D98' paddingBottom={12} fontSize={14}>
                及时获取答案更新通知
              </YTText>
              <YTButton
                maxWidth={400}
                disabled={loading || !url}
                onPress={saveQrCodeToPhotoLibrary}
              >
                {loading ? '加载中...' : '保存二维码'}
              </YTButton>
            </YTYStack>
          </YTYStack>
        </Modal>
      </>
    );
  }),
);

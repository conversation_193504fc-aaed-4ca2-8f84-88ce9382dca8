import { container } from '@jgl/container';
import { AcIcon } from '@jgl/icon/src';
import { useMount } from 'ahooks';
import {
  forwardRef,
  memo,
  PropsWithChildren,
  useCallback,
  useImperativeHandle,
  useState,
} from 'react';
import Modal from 'react-native-modal';
import { Image, Text, View } from 'tamagui';
import { getConfValByCode } from '../api/api';
import { appConfigConfCode } from '../utils/constants';

export type UserGroupModalRef = {
  showModal: () => void;
};

type Props = {};

/** 进用户群弹窗 */
export const UserGroupModal = memo(
  forwardRef<UserGroupModalRef, PropsWithChildren<Props>>((props, ref) => {
    const { children } = props;
    const [open, setOpen] = useState(false);
    const [url, setUrl] = useState('');

    const onClose = useCallback(() => {
      setOpen(false);
    }, [setOpen]);

    const onClickOpen = useCallback(() => {
      setOpen(true);
    }, []);

    useImperativeHandle(ref, () => ({
      showModal: onClickOpen,
    }));

    const getQrCode = useCallback(async () => {
      const res = await container.net().fetch(
        getConfValByCode({
          confCode: appConfigConfCode.userGroupQrcode,
        }),
      );

      const { success, data } = res;
      if (success && data) {
        setUrl(data);
      }
    }, []);

    useMount(() => {
      getQrCode();
    });

    return (
      <>
        <Modal
          onBackdropPress={onClose}
          isVisible={open}
          className='flex-center'
          animationIn='fadeIn'
          animationOut='fadeOut'
          statusBarTranslucent
        >
          <View className='!max-w-[327px] overflow-hidden !rounded-[16px] bg-white'>
            <View className='h-[56px] w-full flex-row items-center justify-between px-[12px]'>
              <View className='h-[32px] w-[32px]' />

              <Text className='text-text' fontWeight={'bold'} fontSize={16}>
                用户群二维码
              </Text>

              <View className='flex-center h-[32px] w-[32px]' onPress={onClose}>
                <Image
                  className='h-[18px] w-[18px]'
                  source={{ uri: AcIcon.IcClose }}
                />
              </View>
            </View>

            <View className='w-full flex-row justify-center'>
              <Image className='h-[180px] w-[180px]' source={{ uri: url }} />
            </View>

            <View className='flex-center mt-[16px] flex-col pb-[24px]'>
              <Text className='text-text-secondary' fontSize={14}>
                扫码进用户群
              </Text>
              <View>
                <Text className='text-text-secondary' fontSize={14}>
                  更多小伙伴在等你哦
                </Text>
              </View>
            </View>
          </View>
        </Modal>

        <View className='flex flex-row items-center' onPress={onClickOpen}>
          {children}
        </View>
      </>
    );
  }),
);

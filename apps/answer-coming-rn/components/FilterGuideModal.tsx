import {
  YTImage,
  YTText,
  YTTouchable,
  YTYStack,
} from '@bookln/cross-platform-components';
import { AcIcon } from '@jgl/icon/src';
import {
  forwardRef,
  memo,
  useCallback,
  useImperativeHandle,
  useState,
} from 'react';
import Modal from 'react-native-modal';

export type FilterGuideModalRef = {
  showModal: () => void;
};

/** 搜索引导提示弹窗 */
export const FilterGuideModal = memo(
  forwardRef<FilterGuideModalRef, {}>((_, ref) => {
    const [open, setOpen] = useState(false);

    const onClose = useCallback(() => {
      setOpen(false);
    }, [setOpen]);

    const onClickOpen = useCallback(() => {
      setOpen(true);
    }, []);

    useImperativeHandle(ref, () => ({
      showModal: onClickOpen,
    }));

    return (
      <Modal
        isVisible={open}
        className='flex-center'
        animationIn='fadeIn'
        animationOut='fadeOut'
        statusBarTranslucent
      >
        <YTYStack ai='center' borderRadius={16} bg='white' p={24}>
          <YTImage w={279} h={341} source={{ uri: AcIcon.FilterGuide }} />

          <YTYStack w='$full' ai='center' pt={12}>
            <YTYStack ai='center' jc='center' mb={24} w='$full'>
              <YTText fontSize={16} color='#1f1f1f'>
                筛选年级、学科、版本
              </YTText>
            </YTYStack>

            <YTTouchable
              bg='$slBlue10'
              ai='center'
              jc='center'
              h={40}
              w={279}
              borderRadius={32}
              onPress={onClose}
            >
              <YTText color={'white'} fontSize={16}>
                学会了
              </YTText>
            </YTTouchable>
          </YTYStack>
        </YTYStack>
      </Modal>
    );
  }),
);

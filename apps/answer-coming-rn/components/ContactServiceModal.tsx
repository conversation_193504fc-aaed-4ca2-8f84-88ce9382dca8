import { container } from '@jgl/container';
import { AcIcon } from '@jgl/icon/src';
import {
  YTButton,
  YTText,
  YTYStack,
  YTXStack,
  YTImage,
} from '@bookln/cross-platform-components';
import { useMount } from 'ahooks';
import {
  forwardRef,
  memo,
  PropsWithChildren,
  useCallback,
  useImperativeHandle,
  useState,
} from 'react';
import Modal from 'react-native-modal';
import { StyleSheet } from 'react-native';
import { cacheDirectory, downloadAsync } from 'expo-file-system';
import {
  requestPermissionsAsync,
  saveToLibraryAsync,
} from 'expo-media-library';
import { showToast } from '@jgl/utils';
import { getConfValByCode } from '../api/api';
import { appConfigConfCode } from '../utils/constants';

export type ContactServiceModalRef = {
  showModal: () => void;
};

type Props = {
  title?: string;
  description?: string;
};

/** 联系客服 */
export const ContactServiceModal = memo(
  forwardRef<ContactServiceModalRef, PropsWithChildren<Props>>((props, ref) => {
    const {
      title = '联系客服',
      description = '扫描二维码添加客服微信',
      children,
    } = props;
    const [open, setOpen] = useState(false);
    const [url, setUrl] = useState('');
    const [loading, setLoading] = useState(false);

    const onClose = useCallback(() => {
      setOpen(false);
    }, [setOpen]);

    const onClickOpen = useCallback(() => {
      setOpen(true);
    }, []);

    useImperativeHandle(ref, () => ({
      showModal: onClickOpen,
    }));

    const getQrCode = useCallback(async () => {
      try {
        setLoading(true);
        const res = await container.net().fetch(
          getConfValByCode({
            confCode: appConfigConfCode.serviceQrcode,
          }),
        );

        const { success, data } = res;
        if (success && data) {
          setUrl(data);
        }
      } catch (error) {
        console.error('获取客服二维码失败:', error);
      } finally {
        setLoading(false);
      }
    }, []);

    /** 保存二维码到相册 */
    const saveQrCodeToPhotoLibrary = useCallback(async () => {
      if (!url) {
        setOpen(false);
        showToast({ title: '二维码未加载完成' });
        return;
      }

      try {
        // 检查媒体库权限
        const { status } = await requestPermissionsAsync();
        if (status !== 'granted') {
          setOpen(false);
          showToast({ title: '需要相册权限才能保存图片' });
          return;
        }

        // 如果是网络图片，先下载到本地缓存
        let localUri = url;
        if (/^https?:\/\//.test(url)) {
          const fileName = `service_qr_${Date.now()}.jpg`;
          const dest = `${cacheDirectory}${fileName}`;
          const downloadResult = await downloadAsync(url, dest);
          if (downloadResult.status !== 200) {
            throw new Error('图片下载失败');
          }
          localUri = downloadResult.uri;
        }

        // 保存图片到相册（需要本地文件 URI）
        await saveToLibraryAsync(localUri);
        setOpen(false);
        showToast({ title: '已保存至相册' });
      } catch (error) {
        console.error('保存客服二维码失败:', error);
        const errorMessage =
          error instanceof Error ? error.message : '保存失败';
        showToast({ title: errorMessage });
      }
    }, [url]);

    useMount(() => {
      getQrCode();
    });
    const styles = StyleSheet.create({
      modalContainer: {
        justifyContent: 'center',
        alignItems: 'center',
      },
    });

    // https://github.com/react-native-modal/react-native-modal/issues/785
    return (
      <>
        <Modal
          onBackdropPress={onClose}
          isVisible={open}
          animationIn='fadeIn'
          animationOut='fadeOut'
          style={styles.modalContainer}
          hardwareAccelerated
          useNativeDriver
          hideModalContentWhileAnimating
          statusBarTranslucent
        >
          <YTYStack
            maxWidth={327}
            overflow='hidden'
            borderRadius={16}
            backgroundColor='white'
            alignSelf='center'
          >
            <YTXStack
              height={56}
              width='100%'
              flexDirection='row'
              alignItems='center'
              justifyContent='space-between'
              paddingHorizontal={12}
            >
              <YTYStack height={32} width={32} />

              <YTText fontWeight='bold' fontSize={16}>
                {title}
              </YTText>

              <YTYStack
                height={32}
                width={32}
                alignItems='center'
                justifyContent='center'
                onPress={onClose}
              >
                <YTImage
                  height={18}
                  width={18}
                  source={{ uri: AcIcon.IcClose }}
                />
              </YTYStack>
            </YTXStack>

            <YTXStack
              width='100%'
              justifyContent='center'
              alignItems='center'
              flexDirection='row'
            >
              <YTImage height={180} width={180} source={{ uri: url }} />
            </YTXStack>

            <YTYStack
              alignItems='center'
              justifyContent='center'
              marginTop={16}
              paddingBottom={24}
            >
              <YTText fontSize={14} color='#8B8D98' paddingBottom={12}>
                {description}
              </YTText>
              <YTButton
                width={200}
                disabled={loading || !url}
                onPress={saveQrCodeToPhotoLibrary}
              >
                {loading ? '加载中...' : '保存二维码'}
              </YTButton>
            </YTYStack>
          </YTYStack>
        </Modal>

        <YTXStack flexDirection='row' alignItems='center' onPress={onClickOpen}>
          {children}
        </YTXStack>
      </>
    );
  }),
);

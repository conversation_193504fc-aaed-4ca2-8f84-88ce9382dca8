import { AcIcon } from '@jgl/icon/src';
import {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { Platform, Pressable, SafeAreaView } from 'react-native';
import { NativeWechatConstants, shareWebpage } from 'native-wechat';
import Modal from 'react-native-modal';
import { Text, View, Image } from 'tamagui';
import { showToast } from '@jgl/utils';
import {
  PermissionPurposeScene,
  usePermission,
  useShowWeChatFeatures,
} from '@jgl/biz-func';
import { initQQ, isQQInstalled, shareToQQ } from '@yunti-private/rn-qq';

type ShareModalProps = {
  // visible: boolean;
  title?: string;
  description?: string;
  coverUrl?: string;
};

export type ShareModalRef = {
  show: () => void;
  close: () => void;
};

/** 分享弹窗 */
export const ShareModal = forwardRef<ShareModalRef, ShareModalProps>(
  (props, ref) => {
    const {
      title = '答案来了',
      description = '整本作业高清答案，快来核对下',
      coverUrl,
    } = props;

    const [isShowShareModal, setIsShowShareModal] = useState(false);
    const showWeChatFeatures = useShowWeChatFeatures();
    const { checkAndRequestPermission } = usePermission();
    const onPressClose = useCallback(() => {
      setIsShowShareModal(false);
    }, []);

    useImperativeHandle(ref, () => ({
      show: () => {
        setIsShowShareModal(true);
      },
      close: () => {
        setIsShowShareModal(false);
      },
    }));

    /**
     * 分享到微信
     */
    const handleShareToWeChat = useCallback(async () => {
      if (showWeChatFeatures) {
        shareWebpage({
          title: title,
          description,
          scene: NativeWechatConstants.WXSceneSession,
          coverUrl: coverUrl
            ? `${coverUrl}?x-oss-process=image/resize,w_200`
            : undefined,
          webpageUrl: 'https://h5mp.guluai.com/app/dall/downloadApp.htm',
        });
      } else {
        showToast({
          title: '请先安装微信',
        });
      }
    }, [showWeChatFeatures, title, description, coverUrl]);

    /**
     * 分享到QQ
     */
    const handleShareToQQ = useCallback(async () => {
      const canContinue =
        Platform.OS === 'android'
          ? await checkAndRequestPermission({
              permissions: ['android.permission.READ_PHONE_STATE'],
              scene: PermissionPurposeScene.QQShare,
            })
          : true;
      if (canContinue) {
        initQQ();
        if (isQQInstalled()) {
          shareToQQ({
            type: 'webpageUrl',
            title,
            description,
            webpageUrl: 'https://h5mp.guluai.com/app/dall/downloadApp.htm',
            imageUrl: coverUrl
              ? `${coverUrl}?x-oss-process=image/resize,w_200`
              : undefined,
          });
        } else {
          showToast({
            title: '请先安装QQ',
          });
        }
      }
    }, [checkAndRequestPermission, coverUrl, description, title]);

    const shareTypeConfig = useMemo(
      () => [
        {
          icon: require('../assets/images/ic_wechat.png'),
          onPress: handleShareToWeChat,
          title: '微信好友',
        },
        {
          icon: require('../assets/images/ic_qq.png'),
          onPress: handleShareToQQ,
          title: 'QQ好友',
        },
      ],
      [handleShareToQQ, handleShareToWeChat],
    );

    return (
      <Modal
        isVisible={isShowShareModal}
        hardwareAccelerated
        hasBackdrop
        animationIn='fadeIn'
        animationOut='fadeOut'
        onBackdropPress={onPressClose}
        style={{
          position: 'absolute',
          bottom: 0,
          margin: 0,
          width: '100%',
        }}
        statusBarTranslucent
      >
        <SafeAreaView className='overflow-hidden rounded-t-2xl bg-white'>
          <View className='flex-row items-center justify-between px-4 py-[13px]'>
            <Text className='text-base font-medium'>请选择分享方式</Text>
            <Pressable className='p-2' onPress={onPressClose}>
              <Image source={{ uri: AcIcon.IcClose }} width={18} height={18} />
            </Pressable>
          </View>
          <View className='flex-row overflow-hidden px-6 pb-12 '>
            {shareTypeConfig.map((item) => (
              <Pressable
                key={item.title}
                onPress={item.onPress}
                className='mr-6 items-center'
              >
                <Image source={item.icon} width={48} height={48} />
                <Text className='mt-2 text-center text-xs text-black'>
                  {item.title}
                </Text>
              </Pressable>
            ))}
          </View>
        </SafeAreaView>
      </Modal>
    );
  },
);

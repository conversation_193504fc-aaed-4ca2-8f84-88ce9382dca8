import Modal from 'react-native-modal';
import { AcIcon } from '@jgl/icon/src';
import { Image, Text, View } from 'tamagui';
import { memo, useCallback, useState } from 'react';

const descArr = [
  '每日限提现1次；',
  '每次最低提现1元；最高提现200元',
  '提现金额无论多少，都需进行审核；提现申请提交后，工作人员会在1-7个工作日进行审核，审核通过后，将直接派发到绑定的微信零钱账号或联系客服，通过微信转账方式打款；',
];

/** 提现说明弹窗 */
export const WithdrawDescModal = memo(() => {
  const [open, setOpen] = useState(false);

  const onClose = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  const onClickOpen = useCallback(() => {
    setOpen(true);
  }, []);

  return (
    <>
      <Image
        className='h-[16px] w-[16px]'
        source={{ uri: AcIcon.IcQuestion }}
        onPress={onClickOpen}
      />

      <Modal
        isVisible={open}
        className='flex-center'
        onBackdropPress={onClose}
        animationIn='fadeIn'
        animationOut='fadeOut'
        statusBarTranslucent
      >
        <View className='max-w-[327px] rounded-2xl bg-white'>
          <View className='h-[56px] w-full flex-row items-center justify-between px-[12px]'>
            <View className='h-[18px] w-[18px]' />

            <Text className='text-text' fontWeight={'bold'} fontSize={16}>
              提现说明
            </Text>

            <Image
              className='h-[18px] w-[18px]'
              src={AcIcon.IcClose}
              source={{ uri: AcIcon.IcClose }}
              onPress={onClose}
            />
          </View>

          <View className=' flex-col p-[24px] pt-0'>
            {descArr.map((item, index) => {
              return (
                <Text
                  key={item}
                  className='text-text-secondary my-[2px]'
                  fontSize={14}
                >
                  {index + 1}.{item}
                </Text>
              );
            })}
          </View>
        </View>
      </Modal>
    </>
  );
});

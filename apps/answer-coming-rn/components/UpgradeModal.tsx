import { useCallback, useMemo } from 'react';
import { TouchableOpacity, View } from 'react-native';
import Modal from 'react-native-modal';
import * as Progress from 'react-native-progress';
import { Button, Image, Text } from 'tamagui';
import { useUpgradeModal } from '../hooks/useUpgradeModal';
import {
  STATUS_FAILED,
  STATUS_NOT_DOWNLOAD,
  STATUS_SUCCESSFUL,
} from '../upgrade/Const';

export const UpgradeModal = () => {
  const {
    isForceInstall,
    market,
    isShowUpgradeModal,
    appVersionDTO,
    downloadStatus,
    onPressClose,
    onUpdatePress,
  } = useUpgradeModal();

  const { name, content } = appVersionDTO ?? {};

  const isDownloading = useMemo(() => {
    const { status } = downloadStatus;
    return (
      status !== STATUS_NOT_DOWNLOAD &&
      status !== STATUS_SUCCESSFUL &&
      status !== STATUS_FAILED
    );
  }, [downloadStatus]);

  const versionLabel = useMemo(() => {
    const { status } = downloadStatus;
    switch (status) {
      case STATUS_NOT_DOWNLOAD:
        return '';
      case STATUS_SUCCESSFUL:
        return '已经下载完成';
      case STATUS_FAILED:
        return '下载出错';
      default:
        return '已更新 ';
    }
  }, [downloadStatus]);

  const renderButtonView = useCallback(
    (isForceInstallAndHasMarket: boolean) => {
      const { progress = 0, total = 0 } = downloadStatus;
      if (isDownloading && total > 0) {
        const percent = total > 0 ? progress / total : 0;
        return (
          <View className={'mx-10 mt-6 flex h-10 flex-row'}>
            <Progress.Bar
              progress={percent}
              animated={false}
              borderWidth={0}
              width={null}
              height={40}
              color={'#FFC80F'}
              unfilledColor={'#F1F1F3'}
              borderRadius={20}
              className='flex-1'
            />

            <View
              className={'absolute h-10 w-full items-center justify-center'}
            >
              <Text className='text-text text-xs'>
                {versionLabel}
                {(percent * 100).toFixed(0)}%
              </Text>
            </View>
          </View>
        );
      }
      return (
        <Button
          onPress={onUpdatePress}
          className={
            isForceInstallAndHasMarket
              ? 'text-text mx-40 mt-6 min-h-[40px] items-center justify-center rounded-xl border-[1px] border-[#E3E4E7] bg-white'
              : 'bg-primary mx-10 mt-6 min-h-[40px] items-center justify-center rounded-xl text-white'
          }
          backgroundColor={isForceInstallAndHasMarket ? '#FFFFFF' : '#4E76FF'}
          borderColor={isForceInstallAndHasMarket ? '#E3E4E7' : 'rgba(0,0,0,0)'}
        >
          下载安装包更新
        </Button>
      );
    },
    [downloadStatus, isDownloading, onUpdatePress, versionLabel],
  );

  const renderBottomView = useCallback(() => {
    let marketview;
    let downloadView;

    // if (market && market?.name) {
    //   marketview = renderMarketButtonView();
    // }
    if (marketview && isForceInstall) {
      downloadView = renderButtonView(true);
    } else {
      if (marketview == null) {
        downloadView = renderButtonView(false);
      }
    }

    return (
      <>
        {marketview}
        {downloadView}
      </>
    );
  }, [isForceInstall, renderButtonView]);

  const renderFooterView = useCallback(() => {
    const contentView =
      isForceInstall || market != null ? undefined : (
        <Text className={'text-text-secondary mt-2 text-xs'}>
          关闭弹窗，后台将继续为您自动更新
        </Text>
      );
    return <View className={'h-[47px] items-center'}>{contentView}</View>;
  }, [isForceInstall, market]);

  const renderCloseView = useCallback(() => {
    if (isForceInstall) {
      return null;
    }
    return (
      <TouchableOpacity
        className={'absolute right-0 h-8 w-8 items-center justify-center'}
        onPress={onPressClose}
        activeOpacity={0.6}
      >
        <Image source={require('../assets/images/upgrade/ic_close.png')} />
      </TouchableOpacity>
    );
  }, [isForceInstall, onPressClose]);

  return (
    <Modal isVisible={isShowUpgradeModal} hasBackdrop statusBarTranslucent>
      <View className='flex items-center justify-center'>
        <View className='w-full max-w-xs rounded-2xl bg-white'>
          <View className='mt-5 items-center'>
            <Text className={'text-primary mx-10 mt-3 text-xs'}>{name}</Text>
            <Text
              className={'text-text mx-10 mt-3 text-[14px]'}
              numberOfLines={6}
            >
              {content}
            </Text>
          </View>
          {/* <View className='h-px w-full bg-[#E0E0E0]' /> */}
          {renderBottomView()}
          {renderFooterView()}
          {renderCloseView()}
        </View>
      </View>
    </Modal>
  );
};

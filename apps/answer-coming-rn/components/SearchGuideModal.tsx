import { AcIcon } from '@jgl/icon/src';
import { forwardRef, useCallback, useImperativeHandle, useState } from 'react';
import Modal from 'react-native-modal';
import { Image, Text, View } from 'tamagui';

export type SearchGuideModalRef = {
  showModal: () => void;
};

/** 搜索引导提示弹窗 */
export const SearchGuideModal = forwardRef<SearchGuideModalRef, {}>(
  (_, ref) => {
    const [open, setOpen] = useState(false);

    const onClose = useCallback(() => {
      setOpen(false);
    }, [setOpen]);

    const onClickOpen = useCallback(() => {
      setOpen(true);
    }, []);

    useImperativeHandle(ref, () => ({
      showModal: onClickOpen,
    }));

    return (
      <Modal
        isVisible={open}
        className='flex-center'
        animationIn='fadeIn'
        animationOut='fadeOut'
        statusBarTranslucent
      >
        <View className='flex flex-col items-center rounded-[16px] bg-white p-[24px]'>
          <View className='flex-center mb-[24px] w-full flex-col'>
            <Text className='text-text font-medium' fontSize={16}>
              只搜书名
            </Text>

            <Text className='text-text-tertiary' fontSize={14}>
              无需输入年级、学科
            </Text>
          </View>

          <Image
            className='h-[106px] w-[279px]'
            source={{ uri: AcIcon.SearchGuide }}
          />

          <View className='w-full flex-col items-center pt-[24px]'>
            <View
              className='bg-primary-hover flex-center h-[40px] w-[279px] rounded-[32px]'
              onPress={onClose}
            >
              <Text className='text-white' fontSize={16}>
                学会了，去搜索
              </Text>
            </View>
          </View>
        </View>
      </Modal>
    );
  },
);

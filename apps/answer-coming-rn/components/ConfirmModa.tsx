import Icon from '@jgl/icon';
import { router } from 'expo-router';
import React, { useCallback, useImperativeHandle, useState } from 'react';
import { Pressable } from 'react-native';
import Modal from 'react-native-modal';
import { Button, Image, Text, View } from 'tamagui';

export type ConfirmModalRef = {
  show: () => void;
  close: () => void;
};

type Props = {
  title: string;
  content: string;
  onPressPositive?: () => void;
  onPressNegative?: () => void;
  negativeText?: string;
  positiveText?: string;
};

/**
 * 确认弹窗
 */
export const ConfirmModal = React.forwardRef<ConfirmModalRef, Props>(
  (props, ref) => {
    const {
      title,
      content,
      onPressPositive,
      onPressNegative,
      negativeText,
      positiveText,
    } = props;
    const [visible, setVisible] = useState(false);

    useImperativeHandle(ref, () => ({
      show: () => {
        setVisible(true);
      },
      close: () => {
        setVisible(false);
      },
    }));

    const onPressClose = useCallback(() => {
      setVisible(false);
      onPressNegative?.();
    }, []);

    return (
      <Modal isVisible={visible} hasBackdrop statusBarTranslucent>
        <View className={'overflow-hidden rounded-2xl bg-white px-6 pb-6'}>
          <View className='flex-row items-center justify-between py-3'>
            <View className='w-6' />
            <Text className='text-base font-bold'>{title}</Text>
            <Pressable onPress={onPressClose}>
              <Image source={{ uri: Icon.close }} width={24} height={24} />
            </Pressable>
          </View>
          <Text className='text-center text-sm'>{content}</Text>
          <View className='mt-6 w-full flex-row gap-x-2 px-6'>
            <View className='flex-1'>
              <Button
                className='text-primary rounded-full bg-white'
                onPress={onPressClose}
              >
                {negativeText}
              </Button>
            </View>
            <View className='flex-1'>
              <Button
                className='bg-primary rounded-full text-white'
                onPress={onPressPositive}
              >
                {positiveText}
              </Button>
            </View>
          </View>
        </View>
      </Modal>
    );
  },
);

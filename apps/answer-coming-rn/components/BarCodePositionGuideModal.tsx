import {
  Y<PERSON><PERSON>,
  YTText,
  YTView,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import { AcIcon } from '@jgl/icon/src';
import { router, storage, useDidHide } from '@jgl/utils';
import { useCountDown, useMount } from 'ahooks';
import {
  forwardRef,
  memo,
  useCallback,
  useImperativeHandle,
  useState,
} from 'react';
import { TouchableOpacity } from 'react-native';
import Modal from 'react-native-modal';
import { routerMap } from '../utils/routerMap';

const firstAutoOpenKey = 'BarCodePositionGuideAutoOpen';

const autoCloseTime = 3000;

export type BarCodePositionGuideModalRef = {
  showModal: (not?: boolean) => void;
};

/** 条形码位置提示弹窗 */
export const BarCodePositionGuideModal = memo(
  forwardRef<BarCodePositionGuideModalRef, {}>((_, ref) => {
    const [open, setOpen] = useState(false);
    const [targetDate, setTargetDate] = useState<number | undefined>(undefined);

    const [notBarcode, setNotBarcode] = useState(false);

    /** 首次使用扫码，默认打开提示弹窗一段时间后自动关闭 */
    useCountDown({
      targetDate,
      onEnd: () => {
        if (open) {
          setOpen(false);
        }
      },
    });

    useMount(async () => {
      const autoOpened = await storage.getItem(firstAutoOpenKey);
      if (!autoOpened) {
        setOpen(true);
        setTargetDate(Date.now() + autoCloseTime);
        await storage.setItem(firstAutoOpenKey, 'true');
      }
    });

    const onClose = useCallback(() => {
      setOpen(false);
      setTargetDate(undefined);
    }, [setOpen]);

    const onClickOpen = useCallback((not = false) => {
      setOpen(true);
      setNotBarcode(not);
    }, []);

    useDidHide(() => {
      onClose();
    });

    useImperativeHandle(ref, () => ({
      showModal: onClickOpen,
    }));

    const handleToSearch = useCallback(() => {
      router.replace(`${routerMap.home}`);
    }, []);

    return (
      <>
        <TouchableOpacity onPress={() => onClickOpen()}>
          <YTImage
            w={24}
            h={24}
            pointerEvents='auto'
            source={{ uri: AcIcon.IcQuestion }}
          />
        </TouchableOpacity>

        <Modal
          isVisible={open}
          className='flex-center'
          onBackdropPress={onClose}
          animationIn='fadeIn'
          animationOut='fadeOut'
          statusBarTranslucent
        >
          <YTYStack
            maxWidth={327}
            overflow='hidden'
            borderRadius={16}
            background={'white'}
            pb={24}
          >
            <YTXStack
              w='$full'
              items={'center'}
              jc='space-between'
              px={12}
              h={notBarcode ? 40 : 56}
            >
              <YTView w={32} h={32} />

              {notBarcode ? null : (
                <YTText fontWeight={'bold'} fontSize={16}>
                  条形码在哪？
                </YTText>
              )}

              <YTYStack jc='center' ai='center' w={32} h={32} onPress={onClose}>
                <YTImage
                  w={18}
                  h={18}
                  pointerEvents='auto'
                  source={{ uri: AcIcon.IcClose }}
                />
              </YTYStack>
            </YTXStack>

            {notBarcode ? (
              <YTXStack ai='center' jc='center' w='$full' mb={12}>
                <YTImage
                  source={{ uri: AcIcon.IcWarning }}
                  w={24}
                  h={24}
                  mr={4}
                />
                <YTText color='#FF4D4F' fontSize={16}>
                  非图书条形码请重新扫码
                </YTText>
              </YTXStack>
            ) : null}

            <YTYStack ai='center' px={24}>
              <YTText textAlign='center' color={'#595959'} fontSize={14}>
                在每本书的背面右下角即可找到条形码，当条形码有残缺或者无法扫码时可以试试「书名搜索」或「输入条形码」
              </YTText>

              <YTImage
                w={250}
                h={114}
                mt={24}
                source={{ uri: AcIcon.BarCodeGuide }}
              />
            </YTYStack>

            {notBarcode ? (
              <YTXStack
                ai='center'
                jc='center'
                mt={8}
                w='$full'
                bg='white'
                flexShrink={0}
              >
                <YTText color={'#595959'} fontSize={14}>
                  没有条形码？
                </YTText>
                <YTYStack ai='center' onPress={handleToSearch}>
                  <YTText mx={4} color='#4096FF' fontSize={14}>
                    搜索书名
                  </YTText>
                </YTYStack>
              </YTXStack>
            ) : null}
          </YTYStack>
        </Modal>
      </>
    );
  }),
);

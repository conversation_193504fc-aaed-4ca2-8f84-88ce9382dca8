import {
  Y<PERSON><PERSON>,
  YTSc<PERSON>View,
  YTStateView,
  YTText,
  YTTouchable,
  YTView,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import { Camera, History } from '@bookln/icon-lucide';
import { AiResultState } from '@jgl/biz-components';
import { CoordTypeEnum, useImageSize, useSafeAreaInsets } from '@jgl/biz-func';
import { useEffect, useMemo, useRef, useState } from 'react';
import { OralCorrectionResult as ResultType } from '../../hooks/useOralCorrection';
import {
  OralCorrectionBottomSheet,
  OralCorrectionBottomSheetRef,
} from './OralCorrectionBottomSheet';

// 手绘风格图标，参照小程序
const HandDrawnIcons = {
  /** 答对绿色的勾 */
  rightGou:
    'https://ytpan.bookln.cn/btpan/secure/stand/product/organize/4/23/195949098_20240108173239_dvrll.svg',
  /** 答错⭕️ */
  errorCircle:
    'https://ytpan.bookln.cn/btpan/secure/stand/product/organize/4/23/195949098_20240108173343_qqpgv.svg',
  /** 问号，未作答 */
  question:
    'https://ytpan.bookln.cn/btpan/secure/stand/product/organize/4/23/195949079_20240731134543_7da8o.png?filename=？.png',
};

interface OralCorrectionResultProps {
  visible: boolean;
  checkDTO?: ResultType;
  loading?: boolean;
  onRetake?: () => void;
  onViewHistory?: () => void;
}

interface ResultInfo {
  coord: number[];
  correct: number;
  judgeResult: number;
  value: string | any; // 可能是字符串或对象
  answer?: Array<{
    recValue: string;
    value: string;
    type?: number;
  }>;
}

export const OralCorrectionResult = (props: OralCorrectionResultProps) => {
  const { visible, checkDTO, loading = false, onRetake, onViewHistory } = props;

  const { bottom } = useSafeAreaInsets();
  const [selectedAnswerItems, setSelectedAnswerItems] = useState<any[]>([]);
  const bottomSheetRef = useRef<OralCorrectionBottomSheetRef>(null);

  // 使用useImageSize hook来处理图片尺寸和缩放
  // 注意：使用originImageUrl计算尺寸，但显示裁剪后的imageUrl
  const { size, init } = useImageSize({ imageUrl: checkDTO?.originImageUrl });

  // 完全参照小程序逻辑：使用checkDTO中的imageUrl（裁剪后的图片）进行显示和标记
  // checkDTO?.imageUrl 是裁剪后的图片，用于显示和标记
  // checkDTO?.originImageUrl 是原图，用于计算尺寸比例
  const displayImageUrl = checkDTO?.imageUrl;

  const rightPercent = useMemo(() => {
    const total = (checkDTO?.rightNums ?? 0) + (checkDTO?.errorNums ?? 0);
    return total > 0 ? ((checkDTO?.rightNums ?? 0) / total) * 100 : 0;
  }, [checkDTO?.rightNums, checkDTO?.errorNums]);

  const renderAllMarks = useMemo(() => {
    if (!checkDTO?.resultInfos || !size.width || !size.height) {
      return null;
    }

    return checkDTO.resultInfos.map((result: any, index: number) => {
      const recResults = result.recResult || [];

      return recResults.map((recResult: ResultInfo, recIndex: number) => {
        const [x1 = 0, y1 = 0, x2 = 0, y2 = 0] = recResult.coord;

        // 使用useImageSize计算的尺寸，与小程序版本保持一致
        const left = size.width * x1;
        const top = size.height * y1;
        const width = size.width * (x2 - x1);
        const height = size.height * (y2 - y1);

        // 参照小程序逻辑判断题目状态
        const isRight = [CoordTypeEnum.Right, CoordTypeEnum.HalfRight].includes(
          recResult.judgeResult,
        );
        const isError = [CoordTypeEnum.Wrong].includes(recResult.judgeResult);
        const notAnswer = [
          CoordTypeEnum.NotContent,
          CoordTypeEnum.QuestionMark,
        ].includes(recResult.judgeResult);

        // 确定显示的图标
        let imageSrc;
        if (isRight) {
          imageSrc = HandDrawnIcons.rightGou;
        } else if (isError) {
          imageSrc = HandDrawnIcons.errorCircle;
        } else if (notAnswer) {
          imageSrc = HandDrawnIcons.question;
        }

        // 如果没有对应的图标，不显示
        if (!imageSrc) {
          return null;
        }

        const handlePress = () => {
          if (isError && recResult.answer) {
            // 点击错误题目，显示底部弹窗
            setSelectedAnswerItems(recResult.answer || []);
            bottomSheetRef.current?.present();
          }
        };

        // 错误题目可点击，其他不可点击
        if (isError) {
          return (
            <YTTouchable
              key={`${index}-${recIndex}`}
              position='absolute'
              style={{
                left,
                top,
                width,
                height,
              }}
              onPress={handlePress}
            >
              <YTView
                width='100%'
                height='100%'
                alignItems='center'
                justifyContent='center'
                backgroundColor={'transparent'}
              >
                <YTImage
                  source={{ uri: imageSrc }}
                  style={{
                    width: '100%',
                    height: 28,
                  }}
                  contentFit='fill'
                />
              </YTView>
            </YTTouchable>
          );
        } else {
          // 正确和未作答题目不可点击
          return (
            <YTXStack
              key={`${index}-${recIndex}`}
              position='absolute'
              style={{
                left,
                top,
                width,
                height,
              }}
              alignItems='center'
              justifyContent='flex-end'
              backgroundColor={'transparent'}
            >
              <YTImage
                source={{ uri: imageSrc }}
                style={{
                  width: 28,
                  height: 28,
                }}
                contentFit='contain'
              />
            </YTXStack>
          );
        }
      });
    });
  }, [checkDTO?.resultInfos, size]);

  const renderResultDescription = useMemo(() => {
    if (!checkDTO?.rightNums && !checkDTO?.errorNums) return null;

    return (
      <YTXStack ai='center' gap={4}>
        <YTText fontSize={18} color='#1f1f1f' fontWeight='bold'>
          正确
        </YTText>
        <YTText fontSize={18} color='#52C41A' fontWeight='bold'>
          {checkDTO?.rightNums}
        </YTText>
        <YTText fontSize={18} color='#1f1f1f' fontWeight='bold'>
          题，
        </YTText>
        <YTText fontSize={16} color='#1f1f1f' fontWeight='bold'>
          错误
        </YTText>
        <YTText fontSize={18} color='#FF4D4F' fontWeight='bold'>
          {checkDTO?.errorNums}
        </YTText>
        <YTText fontSize={18} color='#1f1f1f' fontWeight='bold'>
          题，
        </YTText>
        <YTText fontSize={18} color='#1f1f1f' fontWeight='bold'>
          正确率
        </YTText>
        <YTText fontSize={18} color='#52C41A' fontWeight='bold' ml={4}>
          {rightPercent.toFixed(2)}%
        </YTText>
      </YTXStack>
    );
  }, [checkDTO?.rightNums, checkDTO?.errorNums, rightPercent]);

  const renderContent = useMemo(() => {
    return (
      <YTScrollView flex={1} showsVerticalScrollIndicator={false}>
        <YTYStack flex={1}>
          {/* 图片和标记区域 */}
          <YTView position='relative'>
            {displayImageUrl && size.width > 0 && size.height > 0 ? (
              <YTImage
                source={{ uri: displayImageUrl }}
                style={{
                  width: size.width,
                  height: size.height,
                }}
                contentFit='fill'
              />
            ) : null}

            {renderAllMarks}
          </YTView>

          {/* 结果描述 */}
          <YTYStack ai='center' py={20}>
            {renderResultDescription}
            <YTXStack ai='center' mt={10} gap={4}>
              <YTText color='#4E5969' fontSize={14}>
                点击
              </YTText>

              <YTImage
                source={{ uri: HandDrawnIcons.errorCircle }}
                style={{
                  width: 24,
                  height: 20,
                }}
                contentFit='fill'
              />

              <YTText color='#4E5969' fontSize={14}>
                标记的错误答案题，可查看正确答案
              </YTText>
            </YTXStack>
          </YTYStack>
        </YTYStack>
      </YTScrollView>
    );
  }, [
    displayImageUrl,
    renderAllMarks,
    renderResultDescription,
    size.height,
    size.width,
  ]);

  useEffect(() => {
    init();
  }, [init]);

  if (!visible) {
    return null;
  }

  return (
    <YTYStack flex={1} bg='white' style={{ paddingBottom: bottom || 16 }}>
      <YTStateView
        flex={1}
        width={'$full'}
        overflow={'hidden'}
        isLoading={loading}
        isEmpty={!checkDTO?.resultInfos || checkDTO.resultInfos.length === 0}
        error={checkDTO?.errorMsg ? new Error(checkDTO?.errorMsg) : null}
        emptyProps={{
          render: () => <AiResultState empty={'暂未识别出题目，再拍一次吧~'} />,
        }}
        errorProps={{
          render: () => <AiResultState error={checkDTO?.errorMsg} />,
        }}
      >
        <YTYStack flex={1}>
          {renderContent}

          {/* 底部按钮 */}
          <YTXStack px={16} py={12} gap={12}>
            {onViewHistory ? (
              <YTTouchable onPress={onViewHistory}>
                <YTYStack
                  alignItems={'center'}
                  justifyContent={'center'}
                  backgroundColor={'transparent'}
                >
                  <History size={24} color={'#6F6F6F'} />

                  <YTText color='#6F6F6F' fontSize={12}>
                    批改记录
                  </YTText>
                </YTYStack>
              </YTTouchable>
            ) : null}

            {onRetake ? (
              <YTTouchable
                flex={1}
                py={12}
                backgroundColor={'$yellow9'}
                borderRadius={100}
                alignItems='center'
                onPress={onRetake}
              >
                <YTXStack
                  alignItems={'center'}
                  backgroundColor={'transparent'}
                  gap={4}
                >
                  <Camera size={20} />

                  <YTText color='$yellow12' fontSize={16} fontWeight={'bold'}>
                    再拍一次
                  </YTText>
                </YTXStack>
              </YTTouchable>
            ) : null}
          </YTXStack>
        </YTYStack>
      </YTStateView>

      {/* 底部弹窗 */}
      <OralCorrectionBottomSheet
        ref={bottomSheetRef}
        answerItems={selectedAnswerItems}
      />
    </YTYStack>
  );
};

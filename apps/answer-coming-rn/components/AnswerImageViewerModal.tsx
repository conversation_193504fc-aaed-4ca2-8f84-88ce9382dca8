import React, { useCallback, useImperativeHandle, useState } from 'react';
import { Modal, SafeAreaView } from 'react-native';
import ImageViewer from 'react-native-image-zoom-viewer';
import { IImageInfo } from 'react-native-image-zoom-viewer/built/image-viewer.type';
import { AdBannerView } from './AdBannerView';

type Props = {
  hidePreview?: () => void;
  onIndexChanged?: (index?: number) => void;
};

export type AnswerImageViewerModalRef = {
  show: (index: number, imageUrls: IImageInfo[]) => void;
};

type State = {
  visible: boolean;
  currentIndex: number;
  imageUrls: IImageInfo[];
  adClosed: boolean;
};

/**
 *
 * @returns
 */
export const AnswerImageViewerModal = React.forwardRef<
  AnswerImageViewerModalRef,
  Props
>((props, ref) => {
  const { hidePreview, onIndexChanged } = props;
  const [state, setState] = useState<State>({
    visible: false,
    currentIndex: 0,
    imageUrls: [],
    adClosed: false,
  });

  const { visible, currentIndex, imageUrls, adClosed } = state;

  useImperativeHandle(ref, () => ({
    show: (index: number, images: IImageInfo[]) => {
      setState({
        visible: true,
        currentIndex: index,
        imageUrls: images,
        adClosed: false,
      });
    },
  }));

  const handleHidePreview = () => {
    setState({
      ...state,
      imageUrls: [],
      visible: false,
    });
    hidePreview?.();
  };

  /**
   * 广告关闭
   */
  const handleAdClosed = useCallback(() => {
    setState({
      ...state,
      adClosed: true,
    });
  }, [state]);

  return (
    <Modal visible={visible} statusBarTranslucent>
      <SafeAreaView className='h-full w-full'>
        <ImageViewer
          imageUrls={imageUrls}
          index={currentIndex}
          enableSwipeDown
          show={visible}
          onSwipeDown={handleHidePreview}
          onChange={onIndexChanged}
          key={String(adClosed)}
          swipeDownThreshold={30}
        />
        {adClosed ? undefined : (
          <AdBannerView
            style={{ backgroundColor: 'black' }}
            handleAdClosed={handleAdClosed}
          />
        )}
      </SafeAreaView>
    </Modal>
  );
});

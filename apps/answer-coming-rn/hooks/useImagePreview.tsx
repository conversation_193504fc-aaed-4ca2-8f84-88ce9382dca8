import { YTImage } from '@bookln/cross-platform-components';
import { generateUUID } from '@jgl/utils';
import { useMount } from 'ahooks';
import { useCallback, useState } from 'react';
import { Modal } from 'react-native';
import ImageViewer from 'react-native-image-zoom-viewer';

/**
 * 图片预览
 */
export const useImagePreview = () => {
  const [visible, setVisible] = useState(false);
  const [images, setImages] = useState<{ url: string }[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [uid, setUid] = useState('');

  useMount(() => {
    setUid(generateUUID());
  });

  const show = useCallback((imageUrls: string[], index = 0) => {
    setVisible(true);
    setImages(imageUrls.map((url) => ({ url })));
    setCurrentIndex(index);
  }, []);

  const hide = useCallback(() => {
    setVisible(false);
    setImages([]);
  }, []);

  const PreviewComponent = useCallback(
    () =>
      visible ? (
        <Modal visible={visible} key={uid} statusBarTranslucent>
          <ImageViewer
            imageUrls={images}
            index={currentIndex}
            enableSwipeDown
            show={visible}
            onSwipeDown={hide}
            swipeDownThreshold={30}
            enablePreload
            renderImage={(p) => <YTImage w={'$full'} h={'$full'} {...p} />}
          />
        </Modal>
      ) : null,
    [uid, visible, images, currentIndex, hide],
  );

  return {
    showImagePreview: show,
    ImagePreviewerComponent: PreviewComponent,
  };
};

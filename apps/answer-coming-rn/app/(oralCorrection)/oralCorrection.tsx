import { YTTouchable, YTYStack } from '@bookln/cross-platform-components';
import { ChevronLeftIcon } from '@bookln/icon-lucide';
import { Empty } from '@jgl/components';
import { Stack, router, useNavigation } from 'expo-router';
import { useCallback, useEffect, useMemo } from 'react';
import { OralCorrectionCamera } from '../../components/oralCorrection/OralCorrectionCamera';
import { OralCorrectionResult } from '../../components/oralCorrection/OralCorrectionResult';
import {
  OralCorrectionProgress,
  useOralCorrection,
} from '../../hooks/useOralCorrection';

/**
 * 口算批改页面
 */
const OralCorrectionScreen = () => {
  const { progress, loading, checkDTO, submitCorrection, resetCorrection } =
    useOralCorrection();

  const navigation = useNavigation();

  const handleTakePhoto = useCallback(
    (imageUri: string) => {
      submitCorrection(imageUri);
    },
    [submitCorrection],
  );

  const handleRetake = useCallback(() => {
    resetCorrection();
  }, [resetCorrection]);

  const handleViewHistory = useCallback(() => {
    router.push('/(oralCorrection)/oralCorrectionRecord');
  }, []);

  const onPressBack = useCallback(() => {
    router.back();
  }, []);

  // 根据页面状态动态设置导航栏样式
  useEffect(() => {
    if (progress === OralCorrectionProgress.Camera) {
      // 相机页面使用黑色导航栏
      navigation.setOptions({
        headerStyle: {
          backgroundColor: 'black',
        },
        headerTintColor: 'white',
        headerLeft: () => {
          return (
            <YTTouchable onPress={onPressBack}>
              <ChevronLeftIcon color={'white'} />
            </YTTouchable>
          );
        },
      });
    } else {
      // 其他页面使用白色导航栏
      navigation.setOptions({
        headerStyle: {
          backgroundColor: 'white',
        },
        headerTintColor: 'black',
        headerLeft: () => {
          return (
            <YTTouchable onPress={onPressBack}>
              <ChevronLeftIcon color={'black'} />
            </YTTouchable>
          );
        },
      });
    }
  }, [progress, navigation, onPressBack]);

  const renderContent = useMemo(() => {
    switch (progress) {
      case OralCorrectionProgress.Camera: {
        return (
          <OralCorrectionCamera
            onTakePhoto={handleTakePhoto}
            loading={loading}
          />
        );
      }

      case OralCorrectionProgress.Processing:
      case OralCorrectionProgress.Result: {
        return (
          <OralCorrectionResult
            visible={true}
            checkDTO={checkDTO}
            loading={loading}
            onRetake={handleRetake}
            onViewHistory={handleViewHistory}
          />
        );
      }

      default: {
        return <Empty description={'页面错误'} />;
      }
    }
  }, [
    checkDTO,
    handleRetake,
    handleTakePhoto,
    handleViewHistory,
    loading,
    progress,
  ]);

  return (
    <YTYStack flex={1} bg='white'>
      <Stack.Screen
        options={{
          title:
            progress === OralCorrectionProgress.Camera
              ? '口算批改'
              : '批改结果',
        }}
      />

      {renderContent}
    </YTYStack>
  );
};

export default OralCorrectionScreen;

import {
  Y<PERSON><PERSON>,
  YTScrollView,
  YTStateView,
  YTText,
  YTTouchable,
  YTView,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import { useSafeAreaInsets } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { Stack, router } from 'expo-router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { userCorrectHistory } from '../../api/api';

interface CorrectionRecord {
  id: number;
  recordId?: string;
  sourceUrl?: string;
  gmtCreate: number;
  gmtModified: number;
  rightNums?: number;
  errorNums?: number;
  userId?: number;
}

interface GroupedRecords {
  [date: string]: CorrectionRecord[];
}

/**
 * 口算批改记录页面
 */
const OralCorrectionRecordScreen = () => {
  const { bottom } = useSafeAreaInsets();
  const [recordList, setRecordList] = useState<CorrectionRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 0,
    totalRecords: 0,
  });

  const fetchData = useCallback(
    async (isLoadMore = false) => {
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      try {
        const currentPage = isLoadMore ? pagination.currentPage + 1 : 1;
        const res = await container.net().fetch(
          userCorrectHistory({
            pageSize: 20,
            currentPage,
          }),
        );

        if (res.success && res.data) {
          const {
            pageData = [],
            currentPage: resCurrentPage = 1,
            totalPages = 0,
            totalRecords = 0,
          } = res.data;

          if (isLoadMore) {
            setRecordList((prev) => [...prev, ...pageData]);
          } else {
            setRecordList(pageData);
          }

          setPagination({
            currentPage: resCurrentPage,
            totalPages,
            totalRecords,
          });
        }
      } catch (error) {
        console.error('获取批改记录失败:', error);
      } finally {
        if (isLoadMore) {
          setLoadingMore(false);
        } else {
          setLoading(false);
        }
      }
    },
    [pagination.currentPage],
  );

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleClickImage = useCallback((recordId?: string) => {
    if (recordId) {
      // 跳转到批改结果详情页面，参照小程序逻辑
      router.push(
        `/(oralCorrection)/oralCorrectionResult?recordId=${recordId}`,
      );
    }
  }, []);

  const onScrollToBottom = useCallback(() => {
    if (!loadingMore && pagination.currentPage < pagination.totalPages) {
      fetchData(true);
    }
  }, [loadingMore, pagination.currentPage, pagination.totalPages, fetchData]);

  // 按日期分组记录 - 参考小程序版本的groupByTimestamp函数
  const groupByDate = useCallback(
    (records: CorrectionRecord[]): GroupedRecords => {
      const result: GroupedRecords = {};
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const todayStr = today.toLocaleDateString('zh-CN');
      const yesterdayStr = yesterday.toLocaleDateString('zh-CN');

      for (const record of records) {
        // 使用gmtCreate字段，格式化日期
        const recordDate = new Date(record.gmtCreate);
        let formattedDate = recordDate.toLocaleDateString('zh-CN');

        if (formattedDate === todayStr) {
          formattedDate = '今天';
        } else if (formattedDate === yesterdayStr) {
          formattedDate = '昨天';
        }

        // 初始化分组数组（如果不存在）
        if (!result[formattedDate]) {
          result[formattedDate] = [];
        }

        // 将当前对象添加到对应的日期分组中
        result[formattedDate]?.push(record);
      }

      return result;
    },
    [],
  );

  const content = useMemo(() => {
    if (recordList.length > 0) {
      const groupedRecords = groupByDate(recordList);
      const dateGroups = Object.entries(groupedRecords);

      return dateGroups.map(([date, records]) => (
        <YTYStack key={date} mb={24}>
          {/* 日期标题 */}
          <YTYStack mb={16}>
            <YTText fontSize={16} fontWeight='bold' color='#1f1f1f'>
              {date}
            </YTText>
          </YTYStack>

          {/* 记录网格 */}
          <YTXStack flexWrap='wrap' gap={16}>
            {records.map((record) => (
              <YTTouchable
                key={record.id}
                onPress={() => handleClickImage(record.recordId)}
                style={{
                  width: '30%',
                  aspectRatio: 105 / 140,
                }}
              >
                <YTView
                  w='100%'
                  h='100%'
                  borderRadius={6}
                  borderWidth={1}
                  borderColor='#E5E6EB'
                  overflow='hidden'
                >
                  <YTImage
                    source={{ uri: record.sourceUrl }}
                    style={{
                      width: '100%',
                      height: '100%',
                    }}
                    contentFit='cover'
                  />
                </YTView>
              </YTTouchable>
            ))}
          </YTXStack>
        </YTYStack>
      ));
    }
    return null;
  }, [recordList, groupByDate, handleClickImage]);

  return (
    <YTYStack flex={1} bg='white' style={{ paddingBottom: bottom || 16 }}>
      <Stack.Screen
        options={{
          title: '批改记录',
          headerBackTitle: '返回',
        }}
      />

      <YTStateView
        flex={1}
        width={'$full'}
        overflow={'hidden'}
        isLoading={loading}
        isEmpty={recordList.length === 0}
        emptyProps={{
          message: '暂无批改记录',
        }}
      >
        <YTScrollView
          flex={1}
          showsVerticalScrollIndicator={false}
          onScrollEndDrag={(event) => {
            const { layoutMeasurement, contentOffset, contentSize } =
              event.nativeEvent;
            const isCloseToBottom =
              layoutMeasurement.height + contentOffset.y >=
              contentSize.height - 20;
            if (isCloseToBottom) {
              onScrollToBottom();
            }
          }}
        >
          <YTYStack px={16} py={16}>
            {content}

            {/* 加载更多提示 */}
            {loadingMore && (
              <YTYStack ai='center' py={16}>
                <YTText fontSize={14} color='#999'>
                  加载中...
                </YTText>
              </YTYStack>
            )}

            {pagination.currentPage >= pagination.totalPages &&
              recordList.length > 0 ? (
                <YTYStack ai='center' py={16}>
                  <YTText fontSize={14} color='#999'>
                    没有更多了
                  </YTText>
                </YTYStack>
              )}
          </YTYStack>
        </YTScrollView>
      </YTStateView>
    </YTYStack>
  );
};

export default OralCorrectionRecordScreen;
